{"name": "augmentvip-tools", "displayName": "Augment VIP自动续杯", "description": "一个现代化的自动续杯管理工具", "version": "1.0.0", "publisher": "augmentvip-tools", "icon": "icon.ico", "repository": "https://github.com/Microsoft/vscode-extension-samples/helloworld-sample", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": [], "main": "./out/extension.js", "contributes": {"commands": [{"command": "cardRenewal.openPanel", "title": "打开Augment VIP自动续杯", "category": "Augment VIP自动续杯"}, {"command": "cardRenewal.refresh", "title": "刷新", "icon": "$(refresh)"}], "viewsContainers": {"activitybar": [{"id": "<PERSON><PERSON><PERSON><PERSON>", "title": "Augment VIP续杯", "icon": "$(coffee)"}]}, "views": {"cardRenewal": [{"id": "cardRenewalView", "name": "续杯管理", "when": "true"}]}, "menus": {"view/title": [{"command": "cardRenewal.refresh", "when": "view == cardRenewalView", "group": "navigation"}]}}, "scripts": {"vscode:prepublish": "npm run package", "compile": "webpack", "watch": "webpack --watch", "package": "webpack --mode production --devtool hidden-source-map", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "npm run compile-tests && npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@eslint/js": "^9.13.0", "@stylistic/eslint-plugin": "^2.9.0", "@types/node": "^20", "@types/vscode": "^1.73.0", "@vscode/test-electron": "^2.3.4", "eslint": "^9.13.0", "ts-loader": "^9.4.3", "typescript": "^5.8.3", "typescript-eslint": "^8.26.0", "webpack": "^5.88.0", "webpack-cli": "^5.1.4", "@vscode/vsce": "^2.19.0"}}