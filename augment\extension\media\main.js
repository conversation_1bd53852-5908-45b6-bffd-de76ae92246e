const vscode = acquireVsCodeApi();
document.addEventListener("DOMContentLoaded", function () {
  initializeApp();
  window.addEventListener("message", messageEvent => {
    const messageData = messageEvent.data;
    switch (messageData.command) {
      case "updateMembership":
        updateRemainingDays(messageData.days);
        break;
      case "updateInfo":
        updateInfo(messageData.days, messageData.userNumber, messageData.userAmount);
        break;
      case "updateFullInfo":
        updateFullInfo(messageData.days, messageData.userNumber, messageData.userAmount, messageData.memberType, messageData.memberLevel, messageData.savedCard);
        break;
      case "showError":
        showError(messageData.message);
        break;
      case "getCardCode":
        const cardCodeInput = document.getElementById("cardCodeInput");
        const cardCodeValue = cardCodeInput ? cardCodeInput.value.trim() : "";
        const responseMessage = {
          command: "cardCodeResponse",
          cardCode: cardCodeValue
        };
        vscode.postMessage(responseMessage);
        break;
    }
  });
});
function initializeApp() {
  const appContainer = document.getElementById("app");
  appContainer.innerHTML = createMainInterface();
  bindEventListeners();
  initializeTheme();
  if (window.remainingDays !== undefined) {
    updateRemainingDays(window.remainingDays);
  }
  if (window.userNumber !== undefined && window.userAmount !== undefined) {
    updateDeviceInfo(window.userNumber, window.userAmount);
  }
  if (window.memberType !== undefined) {
    updateMemberType(window.memberType);
  }
  if (window.memberLevel !== undefined) {
    updateMemberLevel(window.memberLevel);
  }
  if (window.savedCard !== undefined && window.savedCard) {
    fillCardCode(window.savedCard);
  }
}
function fillCardCode(cardCode) {
  const cardCodeInput = document.getElementById("cardCodeInput");
  if (cardCodeInput) {
    cardCodeInput.value = cardCode;
  }
}
function showError(errorMessage) {
  const cardCodeInput = document.getElementById("cardCodeInput");
  const errorElement = document.createElement("div");
  errorElement.className = "text-red-500 text-xs mt-2 error-message";
  errorElement.textContent = errorMessage;
  const existingError = document.querySelector(".error-message");
  if (existingError) {
    existingError.remove();
  }
  const parentContainer = cardCodeInput.parentElement;
  parentContainer.appendChild(errorElement);
  cardCodeInput.classList.add("border-red-500");
  // 3秒后移除错误样式
  setTimeout(() => {
    cardCodeInput.classList.remove("border-red-500");
  }, 3000);
}
function updateFullInfo(remainingDays, userNumber, userAmount, memberType, memberLevel, savedCard) {
  updateRemainingDays(remainingDays);
  updateDeviceInfo(userNumber, userAmount);
  updateMemberType(memberType);
  updateMemberLevel(memberLevel);
  if (savedCard) {
    fillCardCode(savedCard);
  }
}
function updateMemberType(memberType) {
  const memberTypeElement = document.querySelector(".member-type");
  if (memberTypeElement) {
    memberTypeElement.textContent = memberType;
  }
}
function updateMemberLevel(memberLevel) {
  const memberLevelElement = document.querySelector(".member-level");
  if (memberLevelElement) {
    memberLevelElement.textContent = memberLevel;
    const badgeElement = document.querySelector(".premium-badge");
    if (badgeElement) {
      if (memberLevel === "高级会员") {
        badgeElement.classList.remove("standard-badge");
        badgeElement.classList.add("premium-badge");
      } else {
        badgeElement.classList.remove("premium-badge");
        badgeElement.classList.add("standard-badge");
      }
    }
  }
}
function updateInfo(remainingDays, userNumber, userAmount) {
  updateRemainingDays(remainingDays);
  updateDeviceInfo(userNumber, userAmount);
}
function updateRemainingDays(remainingDays) {
  const daysRemainingElement = document.querySelector(".days-remaining");
  if (daysRemainingElement) {
    daysRemainingElement.textContent = remainingDays;
    const progressBar = document.querySelector(".bg-gradient-to-r");
    if (progressBar) {
      const progressPercentage = Math.min(100, Math.max(0, remainingDays / 365 * 100));
      progressBar.style.width = progressPercentage + "%";
    }
  }
}
function updateDeviceInfo(userNumber, userAmount) {
  const deviceInfoElement = document.querySelector(".device-info");
  if (deviceInfoElement) {
    const deviceCountElement = deviceInfoElement.querySelector(".device-count");
    if (deviceCountElement) {
      deviceCountElement.textContent = "" + userAmount;
    }
    const deviceTotalElement = deviceInfoElement.querySelector(".device-total");
    if (deviceTotalElement) {
      deviceTotalElement.textContent = "" + userNumber;
    }
  }
}
function createMainInterface() {
  return `
        <!-- 主界面 - 默认为暗色模式 -->
        <div class="w-full max-w-lg mx-auto">
            <div class="glass rounded-2xl overflow-hidden p-6 border border-gray-700">
                <!-- 标题和主题切换 -->
                <div class="flex justify-between items-center mb-6">
                    <div class="flex items-center">
                        <div class="mr-6 text-indigo-400 text-3xl flex-shrink-0">
                            ☕
                        </div>
                        <div class="min-w-0">
                            <h2 class="text-xl font-bold title-gradient whitespace-nowrap">
                                Augment VIP续杯
                            </h2>
                            <div class="version-tag text-gray-500">版本 2.1.0 · <a href="#" id="checkUpdateLink" class="text-indigo-400 hover:text-indigo-300">检查更新</a></div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <!-- 主题切换开关 -->
                        <label class="theme-toggle-switch scale-75">
                            <input type="checkbox" id="themeToggle">
                            <span class="theme-toggle-slider">
                                <span class="text-yellow-400 absolute left-2 top-2 text-xs">☀️</span>
                                <span class="text-blue-400 absolute right-2 top-2 text-xs">🌙</span>
                            </span>
                        </label>
                        <!-- 设置按钮 -->
                        <button id="settingsBtn" class="p-2 rounded-full hover:bg-gray-700/50 transition-all">
                            <span class="text-gray-400 hover:text-gray-200">⚙️</span>
                        </button>
                    </div>
                </div>
                
                <!-- 设备绑定信息 -->
                <div class="bg-gray-800/50 p-3 rounded-lg mb-5 device-info">
                    <div class="flex justify-between items-center">
                        <div>
                            <h4 class="text-xs font-medium text-gray-300">已绑定设备</h4>
                        </div>
                        <div class="flex items-center">
                            <span class="text-sm font-bold text-indigo-400 device-count">0</span>
                            <span class="text-xs text-gray-500 mx-1">/</span>
                            <span class="text-xs text-gray-500 device-total">0</span>
                            <span class="ml-2 text-gray-500 text-sm">💻</span>
                        </div>
                    </div>
                </div>
                
                <!-- 会员状态 -->
                <div class="mb-6">
                    <div class="flex items-center mb-4">
                        <div class="premium-badge text-white p-2 rounded-lg mr-4 scale-hover flex items-center justify-center">
                            <span class="text-lg">👑</span>
                        </div>
                        <div>
                            <h3 class="font-bold text-gray-100 flex items-center whitespace-nowrap">
                                <span class="member-level">高级会员</span>
                                <span class="ml-2 bg-gradient-to-r from-yellow-200 to-yellow-500 bg-clip-text text-transparent text-xs font-normal">尊贵特权</span>
                            </h3>
                            <p class="text-sm text-gray-400"><span class="member-type">年卡会员</span> · 剩余 <span class="days-remaining">320</span> 天</p>
                        </div>
                    </div>
                    <div class="w-full bg-gray-700 rounded-full h-1.5 overflow-hidden">
                        <div class="bg-gradient-to-r from-yellow-400 to-amber-600 h-1.5 rounded-full" style="width: 75%"></div>
                    </div>
                </div>
                
                <!-- 卡密输入 -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-300 mb-2">激活码</label>
                    <input type="text" id="cardCodeInput"
                        class="w-full px-4 py-3 border border-gray-600 bg-gray-800/50 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent input-focus-effect mb-3" 
                        placeholder="请输入卡密...">
                    <button id="renewBtn" class="w-full btn-gradient text-white px-4 py-3 rounded-lg font-medium flex items-center justify-center">
                        立即续杯
                    </button>
                    <button id="activatePrivilegeBtn" class="w-full mt-2 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white px-4 py-3 rounded-lg font-medium flex items-center justify-center transition-all duration-300">
                        激活特权
                    </button>
                    <p class="text-xs text-gray-500 mt-2 text-center">输入激活码以延长您的会员期限</p>
                </div>
                
                <!-- 会员特权提示 -->
                <div class="mt-6 bg-gray-800/30 rounded-lg p-4 border border-gray-700/50">
                    <h4 class="text-sm font-medium text-gray-300 mb-3 flex items-center">
                        <span class="text-indigo-400 mr-2">🎁</span>
                        会员特权
                    </h4>
                    <ul class="space-y-2 text-xs text-gray-400">
                        <li class="flex items-center">
                            <span class="text-green-500 mr-2 text-xs">✓</span>
                            无限额度续杯不限对话次数
                        </li>
                        <li class="flex items-center">
                            <span class="text-green-500 mr-2 text-xs">✓</span>
                            优先获取优质资源与新功能
                        </li>
                        <li class="flex items-center">
                            <span class="text-green-500 mr-2 text-xs">✓</span>
                            解锁续杯工具全部高级功能
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 设置面板 (模态窗口) -->
        <div id="settingsModal" class="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center hidden z-50">
            <div class="bg-gray-800 rounded-xl p-6 w-full max-w-md border border-gray-700">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-bold text-gray-100">设置中心</h3>
                    <button id="closeSettingsBtn" class="text-gray-400 hover:text-gray-200">
                        <span>✖</span>
                    </button>
                </div>
                
                <!-- 色彩主题设置 -->
                <div class="mb-6">
                    <h4 class="text-sm font-medium text-gray-300 mb-3">界面主题色</h4>
                    <div class="grid grid-cols-4 gap-4">
                        <button class="flex flex-col items-center scale-hover theme-color-btn" data-color="indigo">
                            <div class="w-10 h-10 rounded-full bg-indigo-500 mb-2 ring-2 ring-indigo-500 ring-offset-2 ring-offset-gray-800"></div>
                            <span class="text-xs text-gray-400">靛青</span>
                        </button>
                        <button class="flex flex-col items-center scale-hover theme-color-btn" data-color="green">
                            <div class="w-10 h-10 rounded-full bg-green-500 mb-2"></div>
                            <span class="text-xs text-gray-400">翠绿</span>
                        </button>
                        <button class="flex flex-col items-center scale-hover theme-color-btn" data-color="purple">
                            <div class="w-10 h-10 rounded-full bg-purple-500 mb-2"></div>
                            <span class="text-xs text-gray-400">紫罗兰</span>
                        </button>
                        <button class="flex flex-col items-center scale-hover theme-color-btn" data-color="rose">
                            <div class="w-10 h-10 rounded-full bg-rose-500 mb-2"></div>
                            <span class="text-xs text-gray-400">玫瑰红</span>
                        </button>
                    </div>
                </div>

                <!-- 显示模式设置 -->
                <div class="mb-6">
                    <h4 class="text-sm font-medium text-gray-300 mb-3">显示模式</h4>
                    <div class="grid grid-cols-2 gap-3">
                        <button class="px-4 py-3 bg-gray-700 text-gray-200 border border-gray-600 rounded-lg text-sm flex flex-col items-center justify-center mode-dark-btn">
                            <span class="text-blue-400 mb-2 text-lg">🌙</span>
                            <span>深色模式</span>
                        </button>
                        <button class="px-4 py-3 bg-gray-200 text-gray-800 rounded-lg text-sm flex flex-col items-center justify-center mode-light-btn">
                            <span class="text-yellow-500 mb-2 text-lg">☀️</span>
                            <span>浅色模式</span>
                        </button>
                    </div>
                </div>

                <!-- 其他设置 -->
                <div class="mb-6 space-y-4">
                    <div class="flex items-center justify-between bg-gray-700/50 rounded-lg p-3">
                        <div class="flex items-center space-x-3">
                            <span class="text-indigo-400">✨</span>
                            <span class="text-sm text-gray-300">界面动效</span>
                        </div>
                        <label class="relative inline-block w-12 h-6">
                            <input type="checkbox" class="opacity-0 w-0 h-0" checked>
                            <span class="absolute cursor-pointer inset-0 rounded-full bg-gray-600 before:absolute before:content-[''] before:h-5 before:w-5 before:left-0.5 before:bottom-0.5 before:bg-white before:rounded-full before:transition-all checked:before:translate-x-6 checked:bg-indigo-600"></span>
                        </label>
                    </div>

                    <div class="flex items-center justify-between bg-gray-700/50 rounded-lg p-3">
                        <div class="flex items-center space-x-3">
                            <span class="text-indigo-400">🔔</span>
                            <span class="text-sm text-gray-300">到期提醒</span>
                        </div>
                        <label class="relative inline-block w-12 h-6">
                            <input type="checkbox" class="opacity-0 w-0 h-0" checked>
                            <span class="absolute cursor-pointer inset-0 rounded-full bg-gray-600 before:absolute before:content-[''] before:h-5 before:w-5 before:left-0.5 before:bottom-0.5 before:bg-white before:rounded-full before:transition-all checked:before:translate-x-6 checked:bg-indigo-600"></span>
                        </label>
                    </div>
                </div>

                <button id="applySettingsBtn" class="w-full py-3 btn-gradient text-white rounded-lg font-medium">应用设置</button>
            </div>
        </div>
    `;
}
function bindEventListeners() {
  const themeToggle = document.getElementById("themeToggle");
  if (themeToggle) {
    themeToggle.addEventListener("change", toggleTheme);
  }
  const settingsBtn = document.getElementById("settingsBtn");
  const closeSettingsBtn = document.getElementById("closeSettingsBtn");
  const settingsModal = document.getElementById("settingsModal");
  if (settingsBtn) {
    settingsBtn.addEventListener("click", () => {
      settingsModal.classList.toggle("hidden");
      vscode.postMessage({
        command: "openSettings"
      });
    });
  }
  if (closeSettingsBtn) {
    closeSettingsBtn.addEventListener("click", () => {
      settingsModal.classList.add("hidden");
    });
  }
  const modeDarkBtn = document.querySelector(".mode-dark-btn");
  const modeLightBtn = document.querySelector(".mode-light-btn");
  if (modeDarkBtn) {
    modeDarkBtn.addEventListener("click", () => {
      if (themeToggle) {
        themeToggle.checked = false;
        themeToggle.dispatchEvent(new Event("change"));
      }
    });
  }
  if (modeLightBtn) {
    modeLightBtn.addEventListener("click", () => {
      if (themeToggle) {
        themeToggle.checked = true;
        themeToggle.dispatchEvent(new Event("change"));
      }
    });
  }
  const renewBtn = document.getElementById("renewBtn");
  if (renewBtn) {
    renewBtn.addEventListener("click", handleRenewal);
  }
  const activatePrivilegeBtn = document.getElementById("activatePrivilegeBtn");
  if (activatePrivilegeBtn) {
    activatePrivilegeBtn.addEventListener("click", () => {
      vscode.postMessage({
        command: "activatePrivilege"
      });
    });
  }
  const checkUpdateLink = document.getElementById("checkUpdateLink");
  if (checkUpdateLink) {
    checkUpdateLink.addEventListener("click", event => {
      event.preventDefault();
      vscode.postMessage({
        command: "checkUpdate"
      });
    });
  }
  const themeColorBtns = document.querySelectorAll(".theme-color-btn");
  themeColorBtns.forEach(colorBtn => {
    colorBtn.addEventListener("click", () => {
      const colorValue = colorBtn.getAttribute("data-color");
      changeThemeColor(colorValue);
    });
  });
  const applySettingsBtn = document.getElementById("applySettingsBtn");
  if (applySettingsBtn) {
    applySettingsBtn.addEventListener("click", () => {
      document.getElementById("settingsModal").classList.add("hidden");
      vscode.postMessage({
        command: "alert",
        text: "设置已应用"
      });
    });
  }
}
function toggleTheme() {
  const bodyElement = document.body;
  if (this.checked) {
    document.body.classList.remove("dark-mode");
    document.body.classList.add("light-mode");
    document.body.classList.remove("bg-gray-900");
    document.body.classList.add("bg-gray-100");
    vscode.postMessage({
      command: "themeChanged",
      theme: "light"
    });
  } else {
    document.body.classList.add("dark-mode");
    document.body.classList.remove("light-mode");
    document.body.classList.add("bg-gray-900");
    document.body.classList.remove("bg-gray-100");
    vscode.postMessage({
      command: "themeChanged",
      theme: "dark"
    });
  }
}
function handleRenewal() {
  const cardCodeInput = document.getElementById("cardCodeInput");
  if (!cardCodeInput || !cardCodeInput.value.trim()) {
    vscode.postMessage({
      command: "alert",
      text: "请输入有效的激活码"
    });
    return;
  }
  vscode.postMessage({
    command: "renewCard",
    cardCode: cardCodeInput.value.trim()
  });
}
function changeThemeColor(colorName) {
  console.log("Change theme color to:", colorName);
}
function initializeTheme() {
  const currentThemeMode = window.themeMode || "dark";
  if (currentThemeMode === "light") {
    document.body.classList.remove("dark-mode");
    document.body.classList.add("light-mode");
    document.body.classList.remove("bg-gray-900");
    document.body.classList.add("bg-gray-100");
    const themeToggle = document.getElementById("themeToggle");
    if (themeToggle) {
      themeToggle.checked = true;
    }
  } else {
    document.body.classList.add("dark-mode");
    document.body.classList.remove("light-mode");
    document.body.classList.add("bg-gray-900");
    document.body.classList.remove("bg-gray-100");
  }
}
