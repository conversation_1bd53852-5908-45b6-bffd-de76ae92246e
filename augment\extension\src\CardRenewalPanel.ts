import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import * as os from 'os';
import * as https from 'https';
import * as http from 'http';
import { constants } from 'fs';

/**
 * 卡片续费面板类
 */
export class CardRenewalPanel {
  public static currentPanel: CardRenewalPanel | undefined;
  public static readonly viewType = 'cardRenewal';

  private readonly _panel: vscode.WebviewPanel;
  private readonly _extensionUri: vscode.Uri;
  private _disposables: vscode.Disposable[] = [];
  private _remainingDays: number = 0;
  private _userNumber: number = 0;
  private _userAmount: number = 0;
  private _memberType: string = '年卡会员';
  private _memberLevel: string = '高级会员';
  private _savedCard: string = '';

  public static createOrShow(
    extensionUri: vscode.Uri,
    remainingDays: number = 0,
    userNumber: number = 0,
    userAmount: number = 0,
    memberType: string = '年卡会员',
    memberLevel: string = '高级会员',
    savedCard: string = ''
  ) {
    const activeColumn = vscode.window.activeTextEditor
      ? vscode.window.activeTextEditor.viewColumn
      : undefined;

    if (CardRenewalPanel.currentPanel) {
      CardRenewalPanel.currentPanel._panel.reveal(activeColumn);
      CardRenewalPanel.currentPanel._updateInfo(remainingDays, userNumber, userAmount, memberType, memberLevel, savedCard);
      return;
    }

    const webviewPanel = vscode.window.createWebviewPanel(
      CardRenewalPanel.viewType,
      'Augment VIP续杯',
      activeColumn || vscode.ViewColumn.One,
      {
        enableScripts: true,
        localResourceRoots: [
          vscode.Uri.joinPath(extensionUri, 'media'),
          vscode.Uri.joinPath(extensionUri, 'out', 'media')
        ]
      }
    );

    CardRenewalPanel.currentPanel = new CardRenewalPanel(
      webviewPanel,
      extensionUri,
      remainingDays,
      userNumber,
      userAmount,
      memberType,
      memberLevel,
      savedCard
    );
  }

  public static kill() {
    CardRenewalPanel.currentPanel?.dispose();
    CardRenewalPanel.currentPanel = undefined;
  }

  public static revive(webviewPanel: vscode.WebviewPanel, extensionUri: vscode.Uri) {
    CardRenewalPanel.currentPanel = new CardRenewalPanel(webviewPanel, extensionUri);
  }

  private constructor(
    webviewPanel: vscode.WebviewPanel,
    extensionUri: vscode.Uri,
    remainingDays: number = 0,
    userNumber: number = 0,
    userAmount: number = 0,
    memberType: string = '年卡会员',
    memberLevel: string = '高级会员',
    savedCard: string = ''
  ) {
    this._panel = webviewPanel;
    this._extensionUri = extensionUri;
    this._remainingDays = remainingDays;
    this._userNumber = userNumber;
    this._userAmount = userAmount;
    this._memberType = memberType;
    this._memberLevel = memberLevel;
    this._savedCard = savedCard;

    this._update();
    this._autoCheckUpdate();

    if (this._savedCard && this._savedCard.trim() !== '') {
      this._refreshCardInfo();
    }

    this._panel.onDidDispose(() => this.dispose(), null, this._disposables);

    this._panel.webview.onDidReceiveMessage(
      message => {
        switch (message.command) {
          case 'alert':
            vscode.window.showInformationMessage(message.text);
            return;
          case 'renewCard':
            this._handleCardRenewal(message.cardCode);
            return;
          case 'openSettings':
            this._handleOpenSettings();
            return;
          case 'checkUpdate':
            this._handleCheckUpdate();
            return;
          case 'activatePrivilege':
            this._handleActivatePrivilege();
            return;
          case 'themeChanged':
            this._saveThemeMode(message.theme);
            return;
        }
      },
      null,
      this._disposables
    );
  }

  /**
   * 刷新卡密信息
   */
  private async _refreshCardInfo() {
    if (!this._savedCard || this._savedCard.trim() === '') {
      return;
    }

    try {
      const cardInfo = await this._fetchCardInfoFromAPI(this._savedCard);
      this._remainingDays = cardInfo.remaining_days || 0;
      this._userNumber = cardInfo.user_number || 0;
      this._userAmount = cardInfo.bound_devices || 0;

      const useTime = parseInt(cardInfo.usetime) || 0;
      if (useTime >= 365) {
        this._memberType = '年卡会员';
        this._memberLevel = '高级会员';
      } else if (useTime >= 30 && useTime < 60) {
        this._memberType = '月卡会员';
        this._memberLevel = '高级会员';
      } else if (useTime >= 7 && useTime < 30) {
        this._memberType = '周卡会员';
        this._memberLevel = '普通会员';
      } else {
        this._memberType = '天卡会员';
        this._memberLevel = '普通会员';
      }

      this._panel.webview.postMessage({
        command: 'updateFullInfo',
        days: this._remainingDays,
        userNumber: this._userNumber,
        userAmount: this._userAmount,
        memberType: this._memberType,
        memberLevel: this._memberLevel,
        savedCard: this._savedCard
      });

      console.log('成功刷新卡密信息:', cardInfo);
    } catch (error: any) {
      console.log('查询卡密信息失败:', error.message);
    }
  }

  /**
   * 从API获取卡密信息
   */
  private _fetchCardInfoFromAPI(cardCode: string): Promise<any> {
    return new Promise((resolve, reject) => {
      const apiUrl = `http://api.xcdw.asia/augment_cardmi.php?card=${encodeURIComponent(cardCode)}`;
      http.get(apiUrl, response => {
        let responseData = '';
        response.on('data', chunk => {
          responseData += chunk;
        });
        response.on('end', () => {
          try {
            const parsedData = JSON.parse(responseData);
            if (parsedData.status) {
              resolve(parsedData.data);
            } else {
              reject(new Error(parsedData.message || '查询卡密信息失败'));
            }
          } catch (parseError) {
            reject(new Error('解析响应数据失败'));
          }
        });
      }).on('error', requestError => {
        reject(new Error(`请求失败: ${requestError.message}`));
      });
    });
  }

  /**
   * 处理卡片续费
   */
  private async _handleCardRenewal(cardCode: string) {
    if (!cardCode || cardCode.trim() === '') {
      vscode.window.showWarningMessage('请输入有效的激活码');
      return;
    }

    try {
      await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: '正在加密与防护您的代码数据与账号中...',
        cancellable: false
      }, async (progressReporter) => {
        progressReporter.report({ message: '检查设备ID...' });

        let userUuid = '';
        const configFilePath = path.join(os.homedir(), 'augmentPro.json');
        if (fs.existsSync(configFilePath)) {
          try {
            const configData = JSON.parse(fs.readFileSync(configFilePath, 'utf-8'));
            if (configData && configData.user_uuid) {
              userUuid = configData.user_uuid;
            }
          } catch (parseError) {
            // 忽略解析错误
          }
        }

        if (!userUuid) {
          userUuid = this._generateRandomUUID(20);
        }

        progressReporter.report({ message: '请求续杯服务...' });

        const renewalApiUrl = `http://api.xcdw.asia/augment_xbapi.php?card=${encodeURIComponent(cardCode)}&user_uuid=${encodeURIComponent(userUuid)}`;
        const cardInfoResponse = await this._fetchCardInfo(renewalApiUrl);

        if (typeof cardInfoResponse === 'string') {
          const errorMessage = {
            command: 'showError',
            message: cardInfoResponse
          };
          this._panel.webview.postMessage(errorMessage);
          throw new Error(cardInfoResponse);
        }

        const cardData = cardInfoResponse;

        progressReporter.report({ message: '获取当前时间...' });

        const currentTimestamp = await this._fetchTaobaoTime();
        const currentDate = new Date(parseInt(currentTimestamp) || Date.now());
        const formattedDate = this._formatDate(currentDate);

        const endDateTime = new Date(cardData.end_date).getTime();
        const remainingDays = Math.max(0, Math.ceil((endDateTime - currentDate.getTime()) / (24 * 60 * 60 * 1000)));

        const useTime = parseInt(cardData.usetime) || 0;
        let memberType = '';
        let memberLevel = '';

        if (useTime >= 365) {
          memberType = '年卡会员';
        } else if (useTime >= 30 && useTime < 60) {
          memberType = '月卡会员';
        } else if (useTime >= 7 && useTime < 30) {
          memberType = '周卡会员';
        } else {
          memberType = '天卡会员';
        }

        if (useTime >= 30) {
          memberLevel = '高级会员';
        } else {
          memberLevel = '普通会员';
        }

        progressReporter.report({ message: '查找Augment插件...' });

        const augmentPath = this._findAugmentPath();
        if (!augmentPath) {
          throw new Error('未找到augment插件，请确认已安装');
        }

        progressReporter.report({ message: '检查文件权限...' });

        try {
          await this._ensureFileWritable(augmentPath);
        } catch (permissionError: any) {
          throw new Error(`无法修改文件权限: ${permissionError.message}\n请以管理员身份运行或手动设置文件权限`);
        }

        progressReporter.report({ message: '修改插件文件...' });

        let fileContent = fs.readFileSync(augmentPath, 'utf-8');
        const accessTokenBase64 = Buffer.from(cardData.access_token).toString('base64');
        const tenantUriBase64 = Buffer.from(cardData.tenant_uri).toString('base64');

        fileContent = this._removeComments(fileContent, endDateTime);

        const uriHandlerPattern = /\((\w+)\.window\.registerUriHandler/;
        const authRedirectMatch = /(\w+)\.authRedirectURI\.path/.exec(fileContent);

        if (authRedirectMatch && uriHandlerPattern.test(fileContent) && !fileContent.includes('vscode-augment.directLogin')) {
          fileContent = fileContent.replace(
            uriHandlerPattern,
            `($1.commands.registerCommand("vscode-augment.directLogin",function(){${authRedirectMatch[1]}._authSession._context.globalState.update("sessionId",crypto.randomUUID());return ${authRedirectMatch[1]}._authSession.saveSession.apply(${authRedirectMatch[1]},arguments)}),$1.window.registerUriHandler`
          );
        }

        try {
          fs.writeFileSync(augmentPath, fileContent, 'utf-8');
        } catch (writeError: any) {
          throw new Error(`写入文件失败: ${writeError.message}`);
        }

        progressReporter.report({ message: '保存续杯记录...' });

        this._saveInitRecord({
          initialized: true,
          initTime: Date.now(),
          lastRenewTime: Date.now(),
          expireTime: endDateTime,
          card: cardCode,
          user_uuid: userUuid,
          user_number: cardData.user_number,
          user_amount: cardData.user_amount,
          end_date: cardData.end_date,
          memberType: memberType,
          memberLevel: memberLevel,
          version: '1.0.0'
        });

        progressReporter.report({ message: '执行登录...' });

        try {
          const maxRetries = 5;
          let retryCount = 0;
          let loginSuccess = false;

          while (retryCount < maxRetries && !loginSuccess) {
            try {
              const availableCommands = await vscode.commands.getCommands(true);
              const hasDirectLoginCommand = availableCommands.includes('vscode-augment.directLogin');

              if (!hasDirectLoginCommand) {
                retryCount++;
                if (retryCount < maxRetries) {
                  const retryMessage = {
                    message: `等待Augment插件加载 (尝试 ${retryCount}/${maxRetries})...`
                  };
                  progressReporter.report(retryMessage);
                  if (retryCount === 1) {
                    vscode.window.showInformationMessage('正在等待Augment插件加载，请稍候...');
                  }
                  await new Promise(resolve => setTimeout(resolve, 1000));
                  continue;
                } else {
                  throw new Error('Augment插件命令未注册，请重启编辑器后再试');
                }
              }

              await vscode.commands.executeCommand('vscode-augment.directLogin', cardData.access_token, cardData.tenant_uri);
              loginSuccess = true;
            } catch (commandError: any) {
              if (commandError.message && commandError.message.includes('not found') && retryCount < maxRetries) {
                retryCount++;
                const retryMessage = {
                  message: `等待Augment插件加载 (尝试 ${retryCount}/${maxRetries})...`
                };
                progressReporter.report(retryMessage);
                await new Promise(resolve => setTimeout(resolve, retryCount * 1000));
              } else {
                throw commandError;
              }
            }
          }

          const updateMessage = {
            command: 'updateFullInfo',
            days: remainingDays,
            userNumber: cardData.user_number,
            userAmount: cardData.user_amount,
            memberType: memberType,
            memberLevel: memberLevel,
            savedCard: cardCode
          };
          this._panel.webview.postMessage(updateMessage);

          progressReporter.report({
            message: '运行成功！已成功加密代码数据与账号隐私！即将重新加载窗口...'
          });

          vscode.window.showInformationMessage('运行成功！已成功加密代码数据与账号隐私！');

          // 2秒后重新加载窗口
          setTimeout(() => {
            vscode.commands.executeCommand('workbench.action.reloadWindow');
          }, 2000);
        } catch (loginError: any) {
          throw new Error(`登录失败: ${loginError.message}\n如遇到此错误，请重启编辑器后再次点击"立即续杯"按钮`);
        }

        return null;
      });
    } catch (renewalError: any) {
      vscode.window.showErrorMessage(`续杯失败: ${renewalError.message}`);
    }
  }

  /**
   * 生成随机UUID
   */
  private _generateRandomUUID(length: number): string {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
  }

  /**
   * 获取淘宝时间戳
   */
  private _fetchTaobaoTime(): Promise<string> {
    return new Promise((resolve, reject) => {
      https.get('https://acs.m.taobao.com/gw/mtop.common.getTimestamp/', response => {
        let responseData = '';
        response.on('data', chunk => {
          responseData += chunk;
        });
        response.on('end', () => {
          try {
            const parsedData = JSON.parse(responseData);
            if (parsedData && parsedData.data && parsedData.data.t) {
              resolve(parsedData.data.t);
            } else {
              resolve(Date.now().toString());
            }
          } catch (parseError) {
            resolve(Date.now().toString());
          }
        });
      }).on('error', requestError => {
        resolve(Date.now().toString());
      });
    });
  }

  /**
   * 格式化日期
   */
  private _formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }

  /**
   * 获取卡密信息
   */
  private _fetchCardInfo(apiUrl: string): Promise<any> {
    return new Promise((resolve, reject) => {
      http.get(apiUrl, response => {
        let responseData = '';
        response.on('data', chunk => {
          responseData += chunk;
        });
        response.on('end', () => {
          try {
            const parsedData = JSON.parse(responseData);
            if (typeof parsedData === 'string') {
              resolve(parsedData);
            } else {
              resolve(parsedData);
            }
          } catch (parseError) {
            reject(new Error('解析接口返回数据失败'));
          }
        });
      }).on('error', requestError => {
        reject(new Error(`请求卡密接口失败: ${requestError.message}`));
      });
    });
  }

  /**
   * 保存初始化记录
   */
  private _saveInitRecord(recordData: any) {
    const recordPath = this._getInitRecordPath();
    fs.writeFileSync(recordPath, JSON.stringify(recordData, null, 2), 'utf-8');
  }

  /**
   * 查找Augment插件路径
   */
  private _findAugmentPath(): string | null {
    const appName = vscode.env.appName;
    let extensionFolder: string;

    switch (appName) {
      case 'Visual Studio Code':
        extensionFolder = '.vscode';
        break;
      case 'Windsurf':
        extensionFolder = '.windsurf';
        break;
      case 'Cursor':
        extensionFolder = '.cursor';
        break;
      default:
        extensionFolder = '.vscode';
    }

    const extensionsPath = path.join(os.homedir(), extensionFolder, 'extensions');
    if (!fs.existsSync(extensionsPath)) {
      return null;
    }

    const augmentFolder = fs.readdirSync(extensionsPath).find(folderName =>
      folderName.startsWith('augment.vscode-augment-')
    );

    if (!augmentFolder) {
      return null;
    }

    return path.join(extensionsPath, augmentFolder, 'out', 'extension.js');
  }

  /**
   * 获取初始化记录路径
   */
  private _getInitRecordPath(): string {
    return path.join(os.homedir(), 'augmentPro.json');
  }

  /**
   * 更新信息
   */
  private _updateInfo(
    remainingDays: number,
    userNumber: number,
    userAmount: number,
    memberType: string,
    memberLevel: string,
    savedCard: string
  ) {
    this._remainingDays = remainingDays;
    this._userNumber = userNumber;
    this._userAmount = userAmount;
    if (memberType) {
      this._memberType = memberType;
    }
    if (memberLevel) {
      this._memberLevel = memberLevel;
    }
    if (savedCard) {
      this._savedCard = savedCard;
    }

    this._panel.webview.postMessage({
      command: 'updateFullInfo',
      days: remainingDays,
      userNumber: userNumber,
      userAmount: userAmount,
      memberType: this._memberType,
      memberLevel: this._memberLevel,
      savedCard: this._savedCard
    });
  }

  /**
   * 更新剩余天数
   */
  private _updateRemainingDays(remainingDays: number) {
    this._remainingDays = remainingDays;
    const updateMessage = {
      command: 'updateMembership',
      days: remainingDays
    };
    this._panel.webview.postMessage(updateMessage);
  }

  /**
   * 移除注释
   */
  private _removeComments(fileContent: string, expireTime: number = 0): string {
    fileContent = fileContent.replace(/\/\*\*start\*\/[^]*?\/\*\*end\*\//, '');

    let hasModified = false;
    fileContent = fileContent.replace(/super\.report\(([^\}]+)\)\}/, (fullMatch, reportContent) => {
      const vectorMatch = /(\w+)\.toVector\(([^\)]*)\)/.exec(reportContent);
      if (!vectorMatch) {
        return fullMatch;
      }

      const [, vectorVar, vectorArgs] = vectorMatch;
      hasModified = true;
      const randomStr = `"${this._randomString(128, 16)}"`;

      return `/**start*/function hack(e){if(Date.now()>${expireTime})return e;let l=${randomStr},n=0,t={};for(let c in e)l.length>64+n&&(t[c]=e[c].slice(0,-64)+l.slice(n,n+64),n++);return t}/**end*/super.report(hack(${vectorVar}.toVector(${vectorArgs})))}`;
    });

    if (!hasModified) {
      throw new Error('augment插件版本未适配, 请更新插件');
    }

    return fileContent;
  }

  /**
   * 生成随机字符串
   */
  private _randomString(length: number, base: number = 16): string {
    const characters = '0123456789abcdefghijklmnopqrstuvwxyz'.slice(0, base);
    let result = '';
    for (let i = 0; i < length; i++) {
      result += characters[Math.floor(Math.random() * characters.length)];
    }
    return result;
  }

  /**
   * 处理打开设置
   */
  private _handleOpenSettings() {
    vscode.window.showInformationMessage('设置功能暂未实现');
  }

  /**
   * 处理检查更新
   */
  private _handleCheckUpdate() {
    this._checkUpdate(false, true);
  }

  /**
   * 自动检查更新
   */
  private async _autoCheckUpdate() {
    await this._checkUpdate(true, false);
  }

  /**
   * 检查更新
   */
  private async _checkUpdate(isAutoCheck: boolean, isManualCheck: boolean) {
    try {
      const versionApiUrl = 'http://api.xcdw.asia/augment_version.php';
      const versionData = await new Promise<any>((resolve, reject) => {
        http.get(versionApiUrl, response => {
          let responseData = '';
          response.on('data', chunk => {
            responseData += chunk;
          });
          response.on('end', () => {
            try {
              const parsedData = JSON.parse(responseData);
              resolve(parsedData);
            } catch (parseError) {
              reject(new Error('解析版本数据失败'));
            }
          });
        }).on('error', requestError => {
          reject(requestError);
        });
      });

      const currentVersion = 'V2.1.0';
      if (versionData.version && versionData.version !== currentVersion) {
        if (versionData.update_notice) {
          vscode.window.showInformationMessage(versionData.update_notice);
        }
      } else if (isManualCheck) {
        vscode.window.showInformationMessage('当前已是最新版本啦！');
      } else if (isAutoCheck && versionData.notice) {
        vscode.window.showInformationMessage(versionData.notice);
      }
    } catch (error: any) {
      if (isManualCheck) {
        vscode.window.showErrorMessage(`检查更新失败: ${error.message}`);
      }
    }
  }

  /**
   * 处理激活特权
   */
  private async _handleActivatePrivilege() {
    try {
      await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: '正在激活特权...',
        cancellable: false
      }, async (progressReporter) => {
        progressReporter.report({ message: '获取卡密...' });

        return new Promise<void>(resolve => {
          const messageListener = this._panel.webview.onDidReceiveMessage(async message => {
            if (message.command === 'cardCodeResponse') {
              messageListener.dispose();
              const cardCode = message.cardCode;

              if (!cardCode || cardCode.trim() === '') {
                vscode.window.showWarningMessage('请输入有效的激活码');
                resolve();
                return;
              }

              progressReporter.report({ message: '验证卡密...' });

              const verifyApiUrl = `http://api.xcdw.asia/augment_jhapi.php?card=${encodeURIComponent(cardCode)}`;
              try {
                const verifyResult = await new Promise<any>((resolveVerify, rejectVerify) => {
                  http.get(verifyApiUrl, response => {
                    let responseData = '';
                    response.on('data', chunk => {
                      responseData += chunk;
                    });
                    response.on('end', () => {
                      try {
                        const parsedData = JSON.parse(responseData);
                        resolveVerify(parsedData);
                      } catch (parseError) {
                        rejectVerify(new Error('解析响应数据失败'));
                      }
                    });
                  }).on('error', requestError => {
                    rejectVerify(requestError);
                  });
                });

                if (!verifyResult.status) {
                  throw new Error(verifyResult.message || '卡密验证失败');
                }

                progressReporter.report({ message: '检查配置文件...' });

                const configPath = this._getInitRecordPath();
                let userUuid = '';
                let needUpdate = false;

                if (fs.existsSync(configPath)) {
                  try {
                    const configContent = fs.readFileSync(configPath, 'utf-8');
                    const configData = JSON.parse(configContent);
                    if (configData && configData.user_uuid) {
                      userUuid = configData.user_uuid;
                    } else {
                      userUuid = this._generateRandomUUID(20);
                      needUpdate = true;
                    }

                    if (needUpdate) {
                      configData.card = cardCode;
                      configData.user_uuid = userUuid;
                      fs.writeFileSync(configPath, JSON.stringify(configData, null, 2), 'utf-8');
                    }
                  } catch (parseError) {
                    userUuid = this._generateRandomUUID(20);
                    this._saveBasicConfig(cardCode, userUuid);
                  }
                } else {
                  userUuid = this._generateRandomUUID(20);
                  this._saveBasicConfig(cardCode, userUuid);
                }

                progressReporter.report({ message: '查找Augment插件...' });

                const augmentPath = this._findAugmentPath();
                if (!augmentPath) {
                  throw new Error('未找到augment插件，请确认已安装');
                }

                progressReporter.report({ message: '设置文件权限...' });

                try {
                  await this._ensureFileWritable(augmentPath);
                } catch (permissionError: any) {
                  throw new Error(`无法修改文件权限: ${permissionError.message}\n请以管理员身份运行或手动设置文件权限`);
                }

                progressReporter.report({ message: '注入directLogin命令...' });

                let fileContent = fs.readFileSync(augmentPath, 'utf-8');
                const uriHandlerPattern = /\((\w+)\.window\.registerUriHandler/;
                const authRedirectMatch = /(\w+)\.authRedirectURI\.path/.exec(fileContent);

                if (authRedirectMatch && uriHandlerPattern.test(fileContent) && !fileContent.includes('vscode-augment.directLogin')) {
                  fileContent = fileContent.replace(
                    uriHandlerPattern,
                    `($1.commands.registerCommand("vscode-augment.directLogin",function(){${authRedirectMatch[1]}._authSession._context.globalState.update("sessionId",crypto.randomUUID());return ${authRedirectMatch[1]}._authSession.saveSession.apply(${authRedirectMatch[1]},arguments)}),$1.window.registerUriHandler`
                  );
                  fs.writeFileSync(augmentPath, fileContent, 'utf-8');
                }

                progressReporter.report({ message: '执行初次激活...' });

                const defaultAccessToken = 'fhwiafhw4344f9wa3493';
                const defaultTenantUri = 'https://d18.api.augmentcode.com';

                try {
                  const maxRetries = 3;
                  let retryCount = 0;
                  let activationSuccess = false;

                  while (retryCount < maxRetries && !activationSuccess) {
                    try {
                      const availableCommands = await vscode.commands.getCommands(true);
                      const hasDirectLoginCommand = availableCommands.includes('vscode-augment.directLogin');

                      if (!hasDirectLoginCommand) {
                        retryCount++;
                        if (retryCount < maxRetries) {
                          const retryMessage = {
                            message: `等待Augment插件加载 (尝试 ${retryCount}/${maxRetries})...`
                          };
                          progressReporter.report(retryMessage);
                          await new Promise(resolveTimeout => setTimeout(resolveTimeout, 1000));
                          continue;
                        } else {
                          progressReporter.report({
                            message: '命令注册失败，准备重启编辑器...'
                          });
                          setTimeout(() => {
                            vscode.commands.executeCommand('workbench.action.reloadWindow');
                          }, 1000);
                          return;
                        }
                      }

                      await vscode.commands.executeCommand('vscode-augment.directLogin', defaultAccessToken, defaultTenantUri);
                      activationSuccess = true;
                    } catch (commandError: any) {
                      if (commandError.message && commandError.message.includes('not found') && retryCount < maxRetries) {
                        retryCount++;
                        const retryMessage = {
                          message: `等待Augment插件加载 (尝试 ${retryCount}/${maxRetries})...`
                        };
                        progressReporter.report(retryMessage);
                        await new Promise(resolveTimeout => setTimeout(resolveTimeout, retryCount * 1000));
                      } else if (retryCount >= maxRetries - 1) {
                        progressReporter.report({
                          message: '登录尝试失败，准备重启编辑器...'
                        });
                        setTimeout(() => {
                          vscode.commands.executeCommand('workbench.action.reloadWindow');
                        }, 1000);
                        return;
                      } else {
                        retryCount++;
                      }
                    }
                  }
                } catch (loginError) {
                  console.log('预登录尝试完成');
                }

                progressReporter.report({
                  message: '激活特权成功！重新加载窗口...'
                });

                vscode.window.showInformationMessage('激活特权成功，现在可以点击立即续杯啦！');

                setTimeout(() => {
                  vscode.commands.executeCommand('workbench.action.reloadWindow');
                }, 2500);
              } catch (activationError: any) {
                vscode.window.showErrorMessage(`激活特权失败: ${activationError.message}`);
              } finally {
                resolve();
              }
            }
          });

          this._panel.webview.postMessage({
            command: 'getCardCode'
          });
        });
      });
    } catch (privilegeError: any) {
      vscode.window.showErrorMessage(`激活特权失败: ${privilegeError.message}`);
    }
  }

  /**
   * 保存基本配置
   */
  private _saveBasicConfig(cardCode: string, userUuid: string) {
    const configPath = this._getInitRecordPath();
    const basicConfig = {
      initialized: false,
      initTime: Date.now(),
      lastRenewTime: 0,
      expireTime: 0,
      card: cardCode,
      user_uuid: userUuid,
      user_number: 0,
      user_amount: 0,
      end_date: '',
      memberType: '年卡会员',
      memberLevel: '高级会员',
      version: '1.0.0',
      themeMode: this._getThemeMode()
    };
    fs.writeFileSync(configPath, JSON.stringify(basicConfig, null, 2), 'utf-8');
  }

  /**
   * 释放资源
   */
  public dispose() {
    CardRenewalPanel.currentPanel = undefined;
    this._panel.dispose();

    while (this._disposables.length) {
      const disposable = this._disposables.pop();
      if (disposable) {
        disposable.dispose();
      }
    }
  }

  /**
   * 更新界面
   */
  private _update() {
    const webview = this._panel.webview;
    this._panel.webview.html = this._getHtmlForWebview(webview);
  }

  /**
   * 获取主题模式
   */
  private _getThemeMode(): string {
    const configPath = this._getInitRecordPath();
    if (fs.existsSync(configPath)) {
      try {
        const configContent = fs.readFileSync(configPath, 'utf-8');
        const configData = JSON.parse(configContent);
        return configData.themeMode || 'dark';
      } catch (parseError) {
        return 'dark';
      }
    }
    return 'dark';
  }

  /**
   * 保存主题模式
   */
  private _saveThemeMode(themeMode: string) {
    const configPath = this._getInitRecordPath();
    if (fs.existsSync(configPath)) {
      try {
        const configContent = fs.readFileSync(configPath, 'utf-8');
        const configData = JSON.parse(configContent);
        configData.themeMode = themeMode;
        fs.writeFileSync(configPath, JSON.stringify(configData, null, 2), 'utf-8');
      } catch (parseError) {
        // 忽略解析错误
      }
    } else {
      const defaultConfig = {
        initialized: false,
        initTime: Date.now(),
        lastRenewTime: 0,
        expireTime: 0,
        card: '',
        user_uuid: this._generateRandomUUID(20),
        user_number: 0,
        user_amount: 0,
        end_date: '',
        memberType: '年卡会员',
        memberLevel: '高级会员',
        version: '1.0.0',
        themeMode: themeMode
      };
      fs.writeFileSync(configPath, JSON.stringify(defaultConfig, null, 2), 'utf-8');
    }
  }

  /**
   * 获取Webview的HTML内容
   */
  private _getHtmlForWebview(webview: vscode.Webview): string {
    const resetCssUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'reset.css'));
    const vscodeCssUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'vscode.css'));
    const mainCssUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'main.css'));
    const mainJsUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'main.js'));
    const themeMode = this._getThemeMode();
    const nonce = this._getNonce();

    return `<!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline' https://cdn.tailwindcss.com https://fonts.googleapis.com; script-src 'nonce-${nonce}' https://cdn.tailwindcss.com; font-src https://fonts.gstatic.com; img-src ${webview.cspSource} https: data:;">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <script nonce="${nonce}" src="https://cdn.tailwindcss.com"></script>
                <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
                <link href="${resetCssUri}" rel="stylesheet">
                <link href="${vscodeCssUri}" rel="stylesheet">
                <link href="${mainCssUri}" rel="stylesheet">
                <title>Augment VIP续杯</title>
            </head>
            <body class="bg-gray-900 text-gray-100 flex items-center justify-center min-h-screen p-4 dark-mode">
                <div id="app">
                    <!-- 内容将通过JavaScript动态加载 -->
                </div>
                <script nonce="${nonce}">
                    window.remainingDays = ${this._remainingDays};
                    window.userNumber = ${this._userNumber};
                    window.userAmount = ${this._userAmount};
                    window.memberType = "${this._memberType}";
                    window.memberLevel = "${this._memberLevel}";
                    window.savedCard = "${this._savedCard || ''}";
                    window.themeMode = "${themeMode}";
                </script>
                <script nonce="${nonce}" src="${mainJsUri}"></script>
            </body>
            </html>`;
  }

  /**
   * 确保文件可写
   */
  private async _ensureFileWritable(filePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!fs.existsSync(filePath)) {
        return reject(new Error(`文件不存在: ${filePath}`));
      }

      try {
        fs.accessSync(filePath, constants.W_OK);
        return resolve();
      } catch (accessError) {
        try {
          if (process.platform === 'win32') {
            fs.chmodSync(filePath, 0o600);
          } else {
            fs.chmodSync(filePath, 0o666);
          }

          try {
            fs.accessSync(filePath, constants.W_OK);
            return resolve();
          } catch (recheckError) {
            return reject(new Error('修改权限后文件仍然不可写，可能需要管理员权限'));
          }
        } catch (chmodError) {
          if (process.platform === 'win32') {
            return reject(new Error('无法修改文件权限，请尝试以管理员身份运行VS Code/Cursor'));
          } else {
            return reject(new Error('无法修改文件权限，请尝试使用sudo命令运行或手动chmod'));
          }
        }
      }
    });
  }

  /**
   * 生成随机字符串
   */
  private _getNonce(): string {
    let nonce = '';
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 32; i++) {
      nonce += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return nonce;
  }
}
