const path = require('path');

/**@type {import('webpack').Configuration}*/
const config = {
  target: 'node', // VS Code扩展运行在Node.js环境中
  mode: 'none', // 这样可以保留有用的调试信息

  entry: './src/extension.ts', // 扩展的入口点
  output: {
    // 打包后的文件将放在'out'文件夹中，每个文件一个
    path: path.resolve(__dirname, 'out'),
    filename: 'extension.js',
    libraryTarget: 'commonjs2'
  },
  externals: {
    vscode: 'commonjs vscode' // vscode模块是由VS Code运行时创建和注入的
  },
  resolve: {
    // 支持读取TypeScript和JavaScript文件，注意顺序很重要
    extensions: ['.ts', '.js']
  },
  module: {
    rules: [
      {
        test: /\.ts$/,
        exclude: /node_modules/,
        use: [
          {
            loader: 'ts-loader'
          }
        ]
      }
    ]
  },
  devtool: 'nosources-source-map',
  infrastructureLogging: {
    level: "log", // 启用webpack的问题匹配器
  },
};
module.exports = config;