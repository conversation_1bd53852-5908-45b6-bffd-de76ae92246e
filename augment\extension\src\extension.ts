import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import * as https from 'https';
import * as http from 'http';
import { CardRenewalPanel } from './CardRenewalPanel';

/**
 * 获取初始化记录文件路径
 */
function getInitRecordPath(): string {
  return path.join(os.homedir(), 'augmentPro.json');
}

/**
 * 检查是否已初始化
 */
function checkInitialized(): any {
  const configPath = getInitRecordPath();
  if (fs.existsSync(configPath)) {
    try {
      const configContent = fs.readFileSync(configPath, 'utf-8');
      return JSON.parse(configContent);
    } catch (parseError) {
      return null;
    }
  }
  return null;
}

/**
 * 根据使用时间获取会员类型
 */
function getMemberType(useTime: number): string {
  if (useTime >= 365) {
    return '年卡会员';
  } else if (useTime >= 30 && useTime < 60) {
    return '月卡会员';
  } else if (useTime >= 7 && useTime < 30) {
    return '周卡会员';
  } else {
    return '天卡会员';
  }
}

/**
 * 根据使用时间获取会员等级
 */
function getMemberLevel(useTime: number): string {
  if (useTime >= 30) {
    return '高级会员';
  } else {
    return '普通会员';
  }
}

/**
 * 获取淘宝时间戳
 */
function fetchTaobaoTime(): Promise<number> {
  return new Promise(resolve => {
    https.get('https://acs.m.taobao.com/gw/mtop.common.getTimestamp/', response => {
      let responseData = '';
      response.on('data', chunk => {
        responseData += chunk;
      });
      response.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          if (parsedData && parsedData.data && parsedData.data.t) {
            resolve(parseInt(parsedData.data.t));
          } else {
            resolve(Date.now());
          }
        } catch (parseError) {
          resolve(Date.now());
        }
      });
    }).on('error', () => {
      resolve(Date.now());
    });
  });
}

/**
 * 获取卡密信息
 */
function fetchCardInfo(cardCode: string): Promise<any> {
  return new Promise((resolve, reject) => {
    if (!cardCode || cardCode.trim() === '') {
      reject(new Error('卡密不能为空'));
      return;
    }
    const apiUrl = `http://api.xcdw.asia/augment_cardmi.php?card=${encodeURIComponent(cardCode)}`;
    http.get(apiUrl, response => {
      let responseData = '';
      response.on('data', chunk => {
        responseData += chunk;
      });
      response.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          if (parsedData.status) {
            resolve(parsedData.data);
          } else {
            reject(new Error(parsedData.message || '查询卡密信息失败'));
          }
        } catch (parseError) {
          reject(new Error('解析响应数据失败'));
        }
      });
    }).on('error', requestError => {
      reject(new Error(`请求失败: ${requestError.message}`));
    });
  });
}

/**
 * 扩展激活函数
 */
export function activate(context: vscode.ExtensionContext) {
  console.log('Augment VIP 续杯扩展已激活!');
  
  const treeDataProvider = new CardRenewalTreeDataProvider(0, 0, 0, '普通会员');
  const treeViewOptions = {
    treeDataProvider: treeDataProvider,
    showCollapseAll: true
  };
  vscode.window.createTreeView('cardRenewalView', treeViewOptions);
  
  const initData = checkInitialized();
  
  (async () => {
    let remainingDays = 0;
    let userNumber = 0;
    let userAmount = 0;
    let memberType = '年卡会员';
    let memberLevel = '高级会员';
    let savedCard = '';
    
    if (initData && initData.initialized && initData.card) {
      try {
        const cardInfo = await fetchCardInfo(initData.card);
        remainingDays = cardInfo.remaining_days || 0;
        userNumber = cardInfo.user_number || 0;
        userAmount = cardInfo.bound_devices || 0;
        savedCard = initData.card;
        const useTime = parseInt(cardInfo.usetime) || 0;
        
        if (useTime >= 365) {
          memberType = '年卡会员';
          memberLevel = '高级会员';
        } else if (useTime >= 30 && useTime < 60) {
          memberType = '月卡会员';
          memberLevel = '高级会员';
        } else if (useTime >= 7 && useTime < 30) {
          memberType = '周卡会员';
          memberLevel = '普通会员';
        } else {
          memberType = '天卡会员';
          memberLevel = '普通会员';
        }
        console.log('从API获取到最新卡密信息:', cardInfo);
      } catch (apiError) {
        console.log('API查询失败，使用本地缓存数据:', apiError);
        if (initData.end_date) {
          const currentTime = await fetchTaobaoTime();
          const endTime = new Date(initData.end_date).getTime();
          remainingDays = Math.max(0, Math.ceil((endTime - currentTime) / (24 * 60 * 60 * 1000)));
        } else if (initData.expireTime) {
          const currentTime = await fetchTaobaoTime();
          remainingDays = Math.max(0, Math.ceil((initData.expireTime - currentTime) / (24 * 60 * 60 * 1000)));
        }
        userNumber = initData.user_number || 0;
        userAmount = initData.user_amount || 0;
        if (initData.memberType) {
          memberType = initData.memberType;
        }
        if (initData.memberLevel) {
          memberLevel = initData.memberLevel;
        }
        if (initData.card) {
          savedCard = initData.card;
        }
      }
    }
    
    context.globalState.update('augmentRemainingDays', remainingDays);
    context.globalState.update('augmentUserNumber', userNumber);
    context.globalState.update('augmentUserAmount', userAmount);
    context.globalState.update('augmentMemberType', memberType);
    context.globalState.update('augmentMemberLevel', memberLevel);
    context.globalState.update('augmentSavedCard', savedCard);
    
    if (treeDataProvider) {
      treeDataProvider.updateData(remainingDays, userNumber, userAmount, memberLevel);
      treeDataProvider.refresh();
    }
  })();
  
  const openPanelCommand = vscode.commands.registerCommand('cardRenewal.openPanel', async () => {
    const remainingDays = Number(context.globalState.get('augmentRemainingDays')) || 0;
    const userNumber = Number(context.globalState.get('augmentUserNumber')) || 0;
    const userAmount = Number(context.globalState.get('augmentUserAmount')) || 0;
    const memberType = String(context.globalState.get('augmentMemberType') || '年卡会员');
    const memberLevel = String(context.globalState.get('augmentMemberLevel') || '高级会员');
    const savedCard = String(context.globalState.get('augmentSavedCard') || '');
    CardRenewalPanel.createOrShow(context.extensionUri, remainingDays, userNumber, userAmount, memberType, memberLevel, savedCard);
  });
  
  const refreshCommand = vscode.commands.registerCommand('cardRenewal.refresh', async () => {
    const initData = checkInitialized();
    let remainingDays = 0;
    let userNumber = 0;
    let userAmount = 0;
    let memberType = '年卡会员';
    let memberLevel = '高级会员';
    let savedCard = '';
    
    if (initData && initData.initialized && initData.card) {
      try {
        const cardInfo = await fetchCardInfo(initData.card);
        remainingDays = cardInfo.remaining_days || 0;
        userNumber = cardInfo.user_number || 0;
        userAmount = cardInfo.bound_devices || 0;
        savedCard = initData.card;
        const useTime = parseInt(cardInfo.usetime) || 0;
        
        if (useTime >= 365) {
          memberType = '年卡会员';
          memberLevel = '高级会员';
        } else if (useTime >= 30 && useTime < 60) {
          memberType = '月卡会员';
          memberLevel = '高级会员';
        } else if (useTime >= 7 && useTime < 30) {
          memberType = '周卡会员';
          memberLevel = '普通会员';
        } else {
          memberType = '天卡会员';
          memberLevel = '普通会员';
        }
        console.log('刷新时从API获取到最新信息:', cardInfo);
      } catch (apiError) {
        console.log('刷新时API查询失败，使用本地缓存数据:', apiError);
        const currentTime = await fetchTaobaoTime();
        if (initData.end_date) {
          const endTime = new Date(initData.end_date).getTime();
          remainingDays = Math.max(0, Math.ceil((endTime - currentTime) / (24 * 60 * 60 * 1000)));
        } else if (initData.expireTime) {
          remainingDays = Math.max(0, Math.ceil((initData.expireTime - currentTime) / (24 * 60 * 60 * 1000)));
        }
        userNumber = initData.user_number || 0;
        userAmount = initData.user_amount || 0;
        if (initData.memberType) {
          memberType = initData.memberType;
        }
        if (initData.memberLevel) {
          memberLevel = initData.memberLevel;
        }
        if (initData.card) {
          savedCard = initData.card;
        }
      }
    }
    
    context.globalState.update('augmentRemainingDays', remainingDays);
    context.globalState.update('augmentUserNumber', userNumber);
    context.globalState.update('augmentUserAmount', userAmount);
    context.globalState.update('augmentMemberType', memberType);
    context.globalState.update('augmentMemberLevel', memberLevel);
    context.globalState.update('augmentSavedCard', savedCard);
    
    if (CardRenewalPanel.currentPanel) {
      vscode.window.showInformationMessage('刷新 Augment VIP 续杯');
      CardRenewalPanel.kill();
      CardRenewalPanel.createOrShow(context.extensionUri, remainingDays, userNumber, userAmount, memberType, memberLevel, savedCard);
    }
    
    if (treeDataProvider) {
      treeDataProvider.updateData(remainingDays, userNumber, userAmount, memberLevel);
      treeDataProvider.refresh();
    }
  });
  
  context.subscriptions.push(openPanelCommand, refreshCommand);
  
  if (vscode.window.registerWebviewPanelSerializer) {
    vscode.window.registerWebviewPanelSerializer(CardRenewalPanel.viewType, {
      async deserializeWebviewPanel(webviewPanel: vscode.WebviewPanel, state: any) {
        console.log(`恢复webview面板状态: ${state}`);
        CardRenewalPanel.revive(webviewPanel, context.extensionUri);
      }
    });
  }
}

/**
 * 卡片续费树形数据提供者
 */
class CardRenewalTreeDataProvider implements vscode.TreeDataProvider<TreeItem> {
  private _onDidChangeTreeData: vscode.EventEmitter<TreeItem | undefined | null | void> = new vscode.EventEmitter<TreeItem | undefined | null | void>();
  readonly onDidChangeTreeData: vscode.Event<TreeItem | undefined | null | void> = this._onDidChangeTreeData.event;
  
  private _remainingDays: number;
  private _userNumber: number;
  private _userAmount: number;
  private _memberLevel: string;
  
  constructor(remainingDays: number = 0, userNumber: number = 0, userAmount: number = 0, memberLevel: string = '高级会员') {
    this._remainingDays = remainingDays;
    this._userNumber = userNumber;
    this._userAmount = userAmount;
    this._memberLevel = memberLevel;
  }
  
  updateData(remainingDays: number, userNumber: number, userAmount: number, memberLevel: string) {
    this._remainingDays = remainingDays;
    this._userNumber = userNumber;
    this._userAmount = userAmount;
    this._memberLevel = memberLevel;
  }
  
  refresh(): void {
    this._onDidChangeTreeData.fire();
  }
  
  getTreeItem(element: TreeItem): vscode.TreeItem {
    return element;
  }
  
  getChildren(element?: TreeItem): Thenable<TreeItem[]> {
    if (!element) {
      return Promise.resolve([
        new TreeItem(
          '会员状态',
          `${this._memberLevel} · 剩余${this._remainingDays}天`,
          vscode.TreeItemCollapsibleState.None,
          {
            command: 'cardRenewal.openPanel',
            title: '打开 Augment VIP 续杯',
            arguments: []
          }
        ),
        new TreeItem(
          '设备绑定',
          `${this._userAmount}/${this._userNumber} 台设备`,
          vscode.TreeItemCollapsibleState.None
        ),
        new TreeItem(
          '快速续杯',
          '点击打开续杯界面',
          vscode.TreeItemCollapsibleState.None,
          {
            command: 'cardRenewal.openPanel',
            title: '打开 Augment VIP 续杯',
            arguments: []
          }
        )
      ]);
    }
    return Promise.resolve([]);
  }
}

/**
 * 树形项目类
 */
class TreeItem extends vscode.TreeItem {
  constructor(
    public readonly label: string,
    public readonly description: string,
    public readonly collapsibleState: vscode.TreeItemCollapsibleState,
    public readonly command?: vscode.Command
  ) {
    super(label, collapsibleState);
    this.description = description;
    this.tooltip = `${this.label}: ${this.description}`;
  }
}

/**
 * 扩展停用函数
 */
export function deactivate() {
  console.log('Augment VIP 续杯扩展已停用');
}