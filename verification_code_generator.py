#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证码生成器
基于原始JavaScript代码逻辑的Python实现
"""

import time
import math
import json
from datetime import datetime, timedelta
from typing import Dict, Optional, Any


class VerificationCodeGenerator:
    """验证码生成器类"""
    
    def __init__(self):
        # 字符集：排除了容易混淆的字符 0,1,I,O
        self.charset = "23456789ABCDEFGHJKLMNPQRSTUVWXYZ"
        self.code_length = 6
        self.validity_period = 3600000  # 1小时，单位毫秒
        
        print("=== 验证码生成器初始化 ===")
        print(f"字符集: {self.charset}")
        print(f"字符集长度: {len(self.charset)}")
        print(f"验证码长度: {self.code_length}")
        print(f"有效期: {self.validity_period}ms ({self.validity_period//60000}分钟)")
        print("=== 初始化完成 ===")
    
    def generate_code(self, create_timestamp: Optional[int] = None) -> Dict[str, Any]:
        """
        生成验证码
        
        Args:
            create_timestamp: 创建时间戳（毫秒），如果为None则使用当前时间
            
        Returns:
            包含验证码信息的字典
        """
        # 使用当前时间或指定时间
        create_time = create_timestamp if create_timestamp is not None else int(time.time() * 1000)
        expire_time = create_time + self.validity_period
        
        print("=== 新验证码生成调试 ===")
        print(f"创建时间戳: {create_time}")
        print(f"过期时间戳: {expire_time}")
        print(f"创建时间: {datetime.fromtimestamp(create_time/1000).strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"过期时间: {datetime.fromtimestamp(expire_time/1000).strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 转换为秒级时间戳
        second_timestamp = create_time // 1000
        time_code = second_timestamp & 0xFFFF  # 取低16位
        
        print(f"秒级时间戳: {second_timestamp}")
        print(f"时间编码 (16位): {time_code}")
        print(f"时间编码 (二进制): {bin(time_code)[2:].zfill(16)}")
        
        # 生成时间字符（4位）
        time_chars = ""
        temp_code = time_code
        for i in range(4):
            char_index = temp_code % len(self.charset)
            time_chars += self.charset[char_index]
            temp_code = temp_code // len(self.charset)
        
        print(f"时间字符 (4位): {time_chars}")
        
        # 生成校验字符（2位）
        seed_constant = 20241227
        check_seed = (second_timestamp * seed_constant + time_code) % (2**31)
        check_chars = ""
        temp_seed = check_seed
        
        for i in range(2):
            temp_seed = (1664525 * temp_seed + 1013904223 + 7919 * i) % (2**31)
            char_index = abs(temp_seed) % len(self.charset)
            check_chars += self.charset[char_index]
        
        print(f"校验码种子: {check_seed}")
        print(f"校验字符 (2位): {check_chars}")
        
        # 组合最终验证码
        final_code = time_chars + check_chars
        
        print(f"最终验证码: {final_code}")
        print("验证码结构: [时间4位][校验2位]")
        print("=== 调试结束 ===")
        
        return {
            'code': final_code,
            'create_time': create_time,
            'expire_time': expire_time,
            'second_timestamp': second_timestamp,
            'time_code': time_code,
            'time_chars': time_chars,
            'check_chars': check_chars
        }
    
    def create_verification_code(self) -> Dict[str, Any]:
        """
        创建一个新的验证码对象
        
        Returns:
            完整的验证码对象
        """
        current_time = int(time.time() * 1000)
        code_info = self.generate_code(current_time)
        
        return {
            'code': code_info['code'],
            'create_time': code_info['create_time'],
            'expire_time': code_info['expire_time'],
            'second_timestamp': code_info['second_timestamp'],
            'is_valid': True,
            'id': self.generate_id()
        }
    
    def generate_id(self) -> str:
        """生成唯一ID"""
        import random
        import string
        
        timestamp_part = str(int(time.time() * 1000))
        random_part = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
        return f"{timestamp_part}_{random_part}"
    
    def is_code_valid(self, code_obj: Dict[str, Any]) -> bool:
        """
        检查验证码是否有效
        
        Args:
            code_obj: 验证码对象
            
        Returns:
            是否有效
        """
        if not code_obj or not code_obj.get('is_valid', False):
            return False
        
        current_time = int(time.time() * 1000)
        return current_time < code_obj['expire_time']
    
    def get_remaining_time(self, code_obj: Dict[str, Any]) -> int:
        """
        获取验证码剩余有效时间（分钟）
        
        Args:
            code_obj: 验证码对象
            
        Returns:
            剩余分钟数
        """
        if not self.is_code_valid(code_obj):
            return 0
        
        current_time = int(time.time() * 1000)
        remaining_ms = code_obj['expire_time'] - current_time
        return math.ceil(remaining_ms / 60000)
    
    def extract_time_from_code(self, code: str) -> Optional[Dict[str, Any]]:
        """
        从验证码中提取时间信息
        
        Args:
            code: 验证码字符串
            
        Returns:
            时间信息字典或None
        """
        if not code or len(code) != 6:
            return None
        
        try:
            # 提取时间字符（前4位）
            time_chars = code[:4]
            
            # 反向计算时间编码
            time_code = 0
            base = 1
            for char in time_chars:
                if char not in self.charset:
                    return None
                char_index = self.charset.index(char)
                time_code += char_index * base
                base *= len(self.charset)
            
            # 时间编码是16位，需要结合当前时间推算完整时间戳
            current_time = int(time.time())
            current_code = current_time & 0xFFFF
            
            # 计算可能的时间戳
            time_diff = time_code - current_code
            if time_diff > 32768:
                time_diff -= 65536
            elif time_diff < -32768:
                time_diff += 65536
            
            estimated_timestamp = (current_time + time_diff) * 1000
            
            return {
                'timestamp': estimated_timestamp,
                'time_code': time_code,
                'time_chars': time_chars
            }
        except Exception as e:
            print(f"时间提取失败: {e}")
            return None
    
    def verify_code_authenticity(self, code: str, timestamp: Optional[int] = None) -> bool:
        """
        验证验证码的真实性
        
        Args:
            code: 验证码字符串
            timestamp: 可选的时间戳，如果不提供则从验证码中提取
            
        Returns:
            是否为真实验证码
        """
        if not code:
            return False
        
        verify_timestamp = timestamp
        if not verify_timestamp:
            time_info = self.extract_time_from_code(code)
            if not time_info:
                print("无法从验证码中提取时间信息")
                return False
            verify_timestamp = time_info['timestamp']
        
        # 检查时间戳是否在有效期内
        current_time = int(time.time() * 1000)
        time_diff = current_time - verify_timestamp
        
        if time_diff < 0 or time_diff > self.validity_period:
            print(f"时间戳超出有效期: {time_diff}ms")
            return False
        
        # 重新生成验证码进行比较
        expected_code_info = self.generate_code(verify_timestamp)
        expected_code = expected_code_info['code']
        
        print("验证码真实性检查:")
        print(f"  输入验证码: {code}")
        print(f"  期望验证码: {expected_code}")
        print(f"  时间戳: {verify_timestamp}")
        print(f"  验证结果: {code == expected_code}")
        
        return code == expected_code
    
    def format_code_display(self, code: str) -> str:
        """格式化验证码显示"""
        if len(code) == 6:
            return f"{code[:3]} {code[3:]}"
        return code
    
    def format_remaining_time(self, minutes: int) -> str:
        """格式化剩余时间显示"""
        if minutes <= 0:
            return "已过期"
        elif minutes >= 60:
            hours = minutes // 60
            remaining_minutes = minutes % 60
            if remaining_minutes > 0:
                return f"{hours}小时{remaining_minutes}分钟"
            else:
                return f"{hours}小时"
        else:
            return f"{minutes}分钟"


def main():
    """主函数 - 演示验证码生成器的使用"""
    print("🔐 验证码生成器演示")
    print("=" * 50)
    
    # 创建生成器实例
    generator = VerificationCodeGenerator()
    
    # 生成新验证码
    print("\n📝 生成新验证码:")
    code_obj = generator.create_verification_code()
    
    print(f"验证码: {code_obj['code']}")
    print(f"格式化显示: {generator.format_code_display(code_obj['code'])}")
    print(f"创建时间: {datetime.fromtimestamp(code_obj['create_time']/1000).strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"过期时间: {datetime.fromtimestamp(code_obj['expire_time']/1000).strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"剩余时间: {generator.format_remaining_time(generator.get_remaining_time(code_obj))}")
    print(f"是否有效: {'是' if generator.is_code_valid(code_obj) else '否'}")
    print(f"唯一ID: {code_obj['id']}")
    
    # 验证验证码真实性
    print(f"\n🔍 验证码真实性检查:")
    is_authentic = generator.verify_code_authenticity(code_obj['code'], code_obj['create_time'])
    print(f"验证结果: {'通过' if is_authentic else '失败'}")
    
    # 保存到文件
    print(f"\n💾 保存验证码信息到文件:")
    output_data = {
        'verification_code': code_obj,
        'formatted_display': generator.format_code_display(code_obj['code']),
        'remaining_time_text': generator.format_remaining_time(generator.get_remaining_time(code_obj)),
        'generation_time': datetime.now().isoformat(),
        'generator_info': {
            'charset': generator.charset,
            'code_length': generator.code_length,
            'validity_period_minutes': generator.validity_period // 60000
        }
    }
    
    filename = f"verification_code_{int(time.time())}.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)
    
    print(f"验证码信息已保存到: {filename}")
    
    print("\n✅ 演示完成!")


if __name__ == "__main__":
    main()
