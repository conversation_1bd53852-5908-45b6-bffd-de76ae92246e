#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速验证码生成脚本
简化版本，用于快速生成和验证验证码
"""

import time
import math
import argparse
from datetime import datetime


class QuickCodeGenerator:
    """快速验证码生成器"""
    
    def __init__(self):
        self.charset = "23456789ABCDEFGHJKLMNPQRSTUVWXYZ"
        self.validity_period = 3600000  # 1小时
    
    def generate(self, timestamp=None):
        """生成验证码"""
        create_time = timestamp if timestamp else int(time.time() * 1000)
        expire_time = create_time + self.validity_period
        
        # 生成时间字符（4位）
        second_timestamp = create_time // 1000
        time_code = second_timestamp & 0xFFFF
        
        time_chars = ""
        temp = time_code
        for _ in range(4):
            time_chars += self.charset[temp % len(self.charset)]
            temp //= len(self.charset)
        
        # 生成校验字符（2位）
        seed = (second_timestamp * 20241227 + time_code) % (2**31)
        check_chars = ""
        temp_seed = seed
        
        for i in range(2):
            temp_seed = (1664525 * temp_seed + 1013904223 + 7919 * i) % (2**31)
            check_chars += self.charset[abs(temp_seed) % len(self.charset)]
        
        code = time_chars + check_chars
        
        return {
            'code': code,
            'create_time': create_time,
            'expire_time': expire_time,
            'formatted': f"{code[:3]} {code[3:]}",
            'create_time_str': datetime.fromtimestamp(create_time/1000).strftime('%Y-%m-%d %H:%M:%S'),
            'expire_time_str': datetime.fromtimestamp(expire_time/1000).strftime('%Y-%m-%d %H:%M:%S')
        }
    
    def verify(self, code, timestamp=None):
        """验证验证码"""
        if not timestamp:
            # 从验证码提取时间（简化版）
            time_chars = code[:4]
            time_code = 0
            base = 1
            for char in time_chars:
                if char not in self.charset:
                    return False
                time_code += self.charset.index(char) * base
                base *= len(self.charset)
            
            current_time = int(time.time())
            current_code = current_time & 0xFFFF
            time_diff = time_code - current_code
            
            if time_diff > 32768:
                time_diff -= 65536
            elif time_diff < -32768:
                time_diff += 65536
            
            timestamp = (current_time + time_diff) * 1000
        
        # 检查是否过期
        current_time = int(time.time() * 1000)
        if current_time - timestamp > self.validity_period:
            return False
        
        # 重新生成验证码比较
        expected = self.generate(timestamp)
        return code == expected['code']
    
    def remaining_time(self, create_time):
        """计算剩余时间（分钟）"""
        current_time = int(time.time() * 1000)
        expire_time = create_time + self.validity_period
        if current_time >= expire_time:
            return 0
        return math.ceil((expire_time - current_time) / 60000)


def main():
    parser = argparse.ArgumentParser(description='快速验证码生成器')
    parser.add_argument('--generate', '-g', action='store_true', help='生成新验证码')
    parser.add_argument('--verify', '-v', type=str, help='验证指定的验证码')
    parser.add_argument('--batch', '-b', type=int, help='批量生成指定数量的验证码')
    parser.add_argument('--quiet', '-q', action='store_true', help='静默模式，只输出验证码')
    
    args = parser.parse_args()
    generator = QuickCodeGenerator()
    
    if args.generate or (not args.verify and not args.batch):
        # 生成单个验证码
        result = generator.generate()
        
        if args.quiet:
            print(result['code'])
        else:
            print("🔐 验证码生成成功!")
            print(f"验证码: {result['code']}")
            print(f"格式化: {result['formatted']}")
            print(f"生成时间: {result['create_time_str']}")
            print(f"过期时间: {result['expire_time_str']}")
            print(f"有效期: 60分钟")
    
    elif args.verify:
        # 验证验证码
        code = args.verify.replace(' ', '').upper()
        is_valid = generator.verify(code)
        
        if args.quiet:
            print('1' if is_valid else '0')
        else:
            print(f"验证码 {code}: {'✅ 有效' if is_valid else '❌ 无效'}")
    
    elif args.batch:
        # 批量生成
        print(f"📦 批量生成 {args.batch} 个验证码:")
        print("-" * 50)
        
        for i in range(args.batch):
            # 每个验证码间隔1秒生成，确保不同
            if i > 0:
                time.sleep(1)
            
            result = generator.generate()
            if args.quiet:
                print(result['code'])
            else:
                print(f"{i+1:2d}. {result['code']} ({result['formatted']}) - {result['create_time_str']}")


if __name__ == "__main__":
    main()
