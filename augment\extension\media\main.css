/* 导入 TailwindCSS */
@import url('https://cdn.tailwindcss.com');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* 自定义图标样式 */
.icon {
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
}

.icon-coffee::before { content: "☕"; }
.icon-sun::before { content: "☀️"; }
.icon-moon::before { content: "🌙"; }
.icon-settings::before { content: "⚙️"; }
.icon-laptop::before { content: "💻"; }
.icon-crown::before { content: "👑"; }
.icon-gift::before { content: "🎁"; }
.icon-check::before { content: "✅"; }
.icon-close::before { content: "✖️"; }
.icon-bell::before { content: "🔔"; }

/* 自定义样式 */
.glass {
    backdrop-filter: blur(16px);
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.light-mode .glass {
    background-color: rgba(255, 255, 255, 0.92);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.1);
}

.dark-mode .glass {
    background-color: rgba(17, 25, 40, 0.75);
    border: 1px solid rgba(255, 255, 255, 0.125);
}

.premium-badge {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    box-shadow: 0 2px 10px rgba(255, 165, 0, 0.3);
}

.standard-badge {
    background: linear-gradient(135deg, #3498db, #2980b9);
    box-shadow: 0 2px 10px rgba(52, 152, 219, 0.3);
}

body {
    font-family: 'Inter', sans-serif;
    transition: all 0.3s ease;
    margin: 0;
    padding: 0;
    min-height: 100vh;
}

.days-remaining {
    background: linear-gradient(to right, #F59E0B, #FBBF24);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: 700;
    text-shadow: 0px 0px 10px rgba(251, 191, 36, 0.3);
}

.light-mode .days-remaining {
    background: linear-gradient(to right, #D97706, #B45309);
    -webkit-background-clip: text;
    background-clip: text;
    text-shadow: 0px 0px 10px rgba(180, 83, 9, 0.15);
}

.toggle-checkbox:checked {
    right: 0;
    border-color: #4F46E5;
    background-color: #4F46E5;
}

.toggle-label {
    background-color: #374151;
}

.toggle-checkbox:checked + .toggle-label {
    background-color: #4F46E5;
}

.theme-toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.theme-toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.theme-toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #374151;
    transition: .4s;
    border-radius: 34px;
}

.theme-toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .theme-toggle-slider {
    background-color: #4F46E5;
}

input:checked + .theme-toggle-slider:before {
    transform: translateX(26px);
}

.btn-gradient {
    background: linear-gradient(135deg, #4F46E5, #7C3AED);
    transition: all 0.3s ease;
}

.btn-gradient:hover {
    background: linear-gradient(135deg, #4338CA, #6D28D9);
    transform: translateY(-1px);
}

.input-focus-effect:focus {
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
}

.scale-hover:hover {
    transform: scale(1.02);
    transition: all 0.2s ease;
}

.version-tag {
    font-size: 11px;
    opacity: 0.7;
}

/* 亮色模式文本颜色覆盖 */
.light-mode {
    color: #000 !important;
    background-color: #f3f4f6 !important;
}

.light-mode h1, .light-mode h2, .light-mode h3, .light-mode h4, 
.light-mode p, .light-mode span:not(.days-remaining), 
.light-mode label, .light-mode li {
    color: #000 !important;
}

.light-mode .text-gray-300, 
.light-mode .text-gray-400,
.light-mode .text-gray-500,
.light-mode .text-gray-600 {
    color: #111827 !important;
}

.light-mode input {
    color: #000 !important;
}

.light-mode .bg-gray-800\/50 {
    background-color: #f3f4f6 !important;
}

.light-mode .bg-gray-800\/30 {
    background-color: #f9fafb !important;
}

/* VS Code 适配 */
#app {
    padding: 20px;
    max-width: 500px;
    margin: 0 auto;
}

/* 暗色模式默认样式 */
.dark-mode {
    background-color: #1f2937;
    color: #f3f4f6;
}

/* 确保在 VS Code 中正确显示 */
.container {
    width: 100%;
    max-width: 100%;
}

/* 响应式调整 */
@media (max-width: 600px) {
    #app {
        padding: 10px;
    }
}
