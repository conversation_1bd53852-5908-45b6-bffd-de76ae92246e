{"compilerOptions": {"module": "commonjs", "target": "ES2020", "lib": ["ES2020"], "outDir": "out", "sourceMap": true, "strict": true, "rootDir": "src", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "declarationMap": false}, "include": ["src/**/*"], "exclude": ["node_modules", ".vscode-test", "out"]}